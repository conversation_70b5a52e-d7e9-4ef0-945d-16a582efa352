<?php $__env->startSection('title', 'จัดการแพ็กเกจ - Admin Panel'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">
                        <i class="fas fa-box me-2 text-success"></i>จัดการแพ็กเกจ
                    </h1>
                    <p class="text-muted">จัดการแพ็กเกจทั้งหมดของเว็บไซต์</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item">
                            <a href="<?php echo e(route('admin.dashboard')); ?>">
                                <i class="fas fa-home"></i> แดชบอร์ด
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            <i class="fas fa-box"></i> จัดการแพ็กเกจ
                        </li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Action Bar -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="card shadow-sm border-0">
                        <div class="card-body py-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#createPackageModal">
                                        <i class="fas fa-plus"></i> เพิ่มแพ็กเกจใหม่
                                    </button>
                                    <button type="button" class="btn btn-danger me-2" id="bulkDeleteBtn" style="display: none;">
                                        <i class="fas fa-trash"></i> ลบที่เลือก
                                    </button>
                                    <span class="text-muted" id="selectedCount"></span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="input-group" style="width: 300px;">
                                        <input type="text" class="form-control" id="searchInput" placeholder="ค้นหาแพ็กเกจ...">
                                        <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Packages Grid -->
            <div class="row" id="packagesGrid">
                <?php $__empty_1 = true; $__currentLoopData = $packages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $package): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                    <div class="col-lg-4 col-md-6 col-12 mb-4 package-item" data-package-id="<?php echo e($package->id); ?>">
                        <div class="card shadow-sm border-0 h-100 package-card">
                            <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input package-checkbox" type="checkbox" value="<?php echo e($package->id); ?>">
                                </div>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item edit-package" href="#" data-package-id="<?php echo e($package->id); ?>">
                                                <i class="fas fa-edit text-success"></i> แก้ไข
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item view-package" href="<?php echo e(route('packages.show', $package)); ?>" target="_blank">
                                                <i class="fas fa-eye text-info"></i> ดูหน้าบ้าน
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item delete-package" href="#" data-package-id="<?php echo e($package->id); ?>">
                                                <i class="fas fa-trash text-danger"></i> ลบ
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="position-relative">
                                <img src="<?php echo e($package->image_url); ?>" class="card-img-top" style="height: 200px; object-fit: cover;" alt="<?php echo e($package->name); ?>">
                                <div class="position-absolute top-0 end-0 m-2">
                                    <span class="badge bg-success"><?php echo e($package->formatted_price); ?></span>
                                </div>
                            </div>
                            
                            <div class="card-body">
                                <h5 class="card-title"><?php echo e($package->name); ?></h5>
                                <p class="card-text text-muted"><?php echo e(Str::limit($package->description, 100)); ?></p>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i> <?php echo e($package->created_at->format('d/m/Y')); ?>

                                    </small>
                                    <div>
                                        <button class="btn btn-sm btn-success edit-package" data-package-id="<?php echo e($package->id); ?>">
                                            <i class="fas fa-edit"></i> แก้ไข
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body text-center py-5">
                                <i class="fas fa-box fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">ยังไม่มีแพ็กเกจ</h5>
                                <p class="text-muted">เริ่มต้นด้วยการเพิ่มแพ็กเกจแรกของคุณ</p>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createPackageModal">
                                    <i class="fas fa-plus"></i> เพิ่มแพ็กเกจใหม่
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
</div>

<!-- Create/Edit Package Modal -->
<div class="modal fade" id="createPackageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> <span id="modalTitle">เพิ่มแพ็กเกจใหม่</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="packageForm" enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="packageId" name="package_id">
                <input type="hidden" id="formMethod" name="_method" value="POST">
                
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">ชื่อแพ็กเกจ <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">รายละเอียดแพ็กเกจ <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="price" class="form-label">ราคา (บาท) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="price" name="price" min="0" step="0.01" required>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="image" class="form-label">รูปภาพแพ็กเกจ</label>
                                <input type="file" class="form-control" id="image" name="image" accept="image/*">
                                <small class="form-text text-muted">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB)</small>
                            </div>
                            
                            <div id="imagePreview" class="text-center" style="display: none;">
                                <img id="previewImg" src="" class="img-thumbnail" style="max-width: 100%; max-height: 200px;">
                                <div class="mt-2">
                                    <button type="button" class="btn btn-sm btn-danger" id="removeImage">
                                        <i class="fas fa-trash"></i> ลบรูปภาพ
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ยกเลิก</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> <span id="submitBtnText">บันทึก</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
.package-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}
.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}
.package-checkbox:checked ~ .card {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,.25);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        let searchTerm = $(this).val().toLowerCase();
        $('.package-item').each(function() {
            let packageName = $(this).find('.card-title').text().toLowerCase();
            let packageDesc = $(this).find('.card-text').text().toLowerCase();

            if (packageName.includes(searchTerm) || packageDesc.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Checkbox selection
    $('.package-checkbox').on('change', function() {
        updateBulkActions();
    });

    function updateBulkActions() {
        let checkedBoxes = $('.package-checkbox:checked');
        if (checkedBoxes.length > 0) {
            $('#bulkDeleteBtn').show();
            $('#selectedCount').text(`เลือกแล้ว ${checkedBoxes.length} รายการ`);
        } else {
            $('#bulkDeleteBtn').hide();
            $('#selectedCount').text('');
        }
    }

    // Edit package
    $('.edit-package').on('click', function(e) {
        e.preventDefault();
        let packageId = $(this).data('package-id');
        loadPackageData(packageId);
    });

    function loadPackageData(packageId) {
        $.get(`/admin/packages/${packageId}`, function(data) {
            $('#packageId').val(data.id);
            $('#name').val(data.name);
            $('#description').val(data.description);
            $('#price').val(data.price);
            $('#formMethod').val('PUT');
            $('#modalTitle').text('แก้ไขแพ็กเกจ');
            $('#submitBtnText').text('อัปเดต');

            if (data.image_url) {
                $('#previewImg').attr('src', data.image_url);
                $('#imagePreview').show();
            }

            $('#createPackageModal').modal('show');
        });
    }

    // Reset modal when closed
    $('#createPackageModal').on('hidden.bs.modal', function() {
        $('#packageForm')[0].reset();
        $('#packageId').val('');
        $('#formMethod').val('POST');
        $('#modalTitle').text('เพิ่มแพ็กเกจใหม่');
        $('#submitBtnText').text('บันทึก');
        $('#imagePreview').hide();
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    });

    // Image preview
    $('#image').on('change', function() {
        let file = this.files[0];
        if (file) {
            let reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                $('#imagePreview').show();
            };
            reader.readAsDataURL(file);
        }
    });

    // Remove image
    $('#removeImage').on('click', function() {
        $('#image').val('');
        $('#imagePreview').hide();
    });

    // Submit form
    $('#packageForm').on('submit', function(e) {
        e.preventDefault();

        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').remove();

        let formData = new FormData(this);
        let packageId = $('#packageId').val();
        let method = $('#formMethod').val();
        let url = packageId ? `/admin/packages/${packageId}` : '/admin/packages';

        if (method === 'PUT') {
            formData.append('_method', 'PUT');
        }

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#createPackageModal').modal('hide');
                location.reload();
            },
            error: function(xhr) {
                if (xhr.responseJSON && xhr.responseJSON.errors) {
                    let errors = xhr.responseJSON.errors;
                    Object.keys(errors).forEach(function(key) {
                        $(`#${key}`).addClass('is-invalid');
                        $(`#${key}`).after(`<div class="invalid-feedback">${errors[key][0]}</div>`);
                    });
                }
            }
        });
    });

    // Delete package
    $('.delete-package').on('click', function(e) {
        e.preventDefault();
        let packageId = $(this).data('package-id');

        if (confirm('คุณแน่ใจหรือไม่ที่จะลบแพ็กเกจนี้?')) {
            $.ajax({
                url: `/admin/packages/${packageId}`,
                type: 'DELETE',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function() {
                    location.reload();
                }
            });
        }
    });

    // Bulk delete
    $('#bulkDeleteBtn').on('click', function() {
        let selectedIds = $('.package-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (confirm(`คุณแน่ใจหรือไม่ที่จะลบแพ็กเกจ ${selectedIds.length} รายการ?`)) {
            $.ajax({
                url: '/admin/packages/bulk-delete',
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content'),
                    ids: selectedIds
                },
                success: function() {
                    location.reload();
                }
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/packages/index.blade.php ENDPATH**/ ?>