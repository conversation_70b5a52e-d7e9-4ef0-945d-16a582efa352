<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

class ServiceController extends BaseResourceController
{
    protected function getModelClass()
    {
        return Service::class;
    }

    protected function getViewPrefix()
    {
        return 'services';
    }

    protected function getRoutePrefix()
    {
        return 'services';
    }

    protected function getImageDirectory()
    {
        return 'services';
    }

    protected function getValidationRules(Request $request, $model = null)
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'required|numeric|min:0',
            'image' => 'nullable|image|mimes:jpeg,jpg,png,gif,webp|max:2048',
        ];
    }

    // หน้าบ้าน: แสดงรายละเอียดบริการ
    public function show(Service $service)
    {
        // Handle AJAX request for admin
        $ajaxResponse = $this->handleAjaxShow($service, [
            'title', 'description', 'price', 'image_url'
        ]);

        if ($ajaxResponse) {
            return $ajaxResponse;
        }

        return view('services.show', compact('service'));
    }

    // Store method for creating new service
    public function store(Request $request)
    {
        $data = $request->validate($this->getValidationRules($request));

        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = ImageHelper::uploadImage($request->file('image'), $this->getImageDirectory());
        }

        $service = Service::create($data);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'เพิ่มบริการสำเร็จ',
                'service' => $service
            ]);
        }

        return redirect()->route('admin.services.index')->with('success', 'เพิ่มบริการสำเร็จ');
    }

    // Override update method to handle AJAX requests
    public function update(Request $request, $model)
    {
        $data = $request->validate($this->getValidationRules($request, $model));

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($model->image) {
                ImageHelper::deleteImage($model->image);
            }
            $data['image'] = ImageHelper::uploadImage($request->file('image'), $this->getImageDirectory());
        }

        $model->update($data);

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'message' => 'อัปเดตบริการสำเร็จ',
                'service' => $model,
                'image_url' => $model->image_url
            ]);
        }

        return redirect()->route('admin.services.index')->with('success', 'อัปเดตบริการสำเร็จ');
    }

    // หลังบ้าน: ลบบริการหลายรายการ
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:services,id'
        ]);

        try {
            $services = Service::whereIn('id', $request->ids)->get();

            // Delete images first
            foreach ($services as $service) {
                if ($service->image) {
                    ImageHelper::deleteImage($service->image);
                }
            }

            // Delete services
            Service::whereIn('id', $request->ids)->delete();

            return response()->json([
                'success' => true,
                'message' => 'ลบบริการ ' . count($request->ids) . ' รายการสำเร็จ'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'เกิดข้อผิดพลาดในการลบข้อมูล'
            ], 500);
        }
    }
}